@extends('backend.layouts.app')
@section('title', 'Admin Dashboard')
@section('content')
<style>
    .gradient-success { background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%) !important; }
    .gradient-primary { background: linear-gradient(90deg, #396afc 0%, #2948ff 100%) !important; }
    .gradient-warning { background: linear-gradient(90deg, #f7971e 0%, #ffd200 100%) !important; }
    .gradient-danger  { background: linear-gradient(90deg, #f953c6 0%, #b91d73 100%) !important; }
    .gradient-info    { background: linear-gradient(90deg, #43cea2 0%, #185a9d 100%) !important; }
    .gradient-secondary { background: linear-gradient(90deg, #bdc3c7 0%, #2c3e50 100%) !important; }
    .dashboard-card {
        color: #fff;
        border: none;
        min-height: 110px;
    }
    .dashboard-icon {
        font-size: 2.5rem;
        opacity: 0.85;
    }
    .bg-white.rounded.shadow-sm.p-4 {
        margin-bottom: 2rem;
    }
</style>
<div class="container-fluid">

    <div class="row mb-4">
        @include('backend.default_alert')
    </div>

    <!-- ===================== USERS SECTION ===================== -->
    <div class="bg-white rounded shadow-sm p-4 mb-4">
        <div class="row mb-3">
            <div class="col-12">
                <h4 class="font-weight-bold text-primary mb-0">
                    <i class="mdi mdi-account-multiple"></i> Users Overview
                </h4>
            </div>
        </div>
        <div class="row">

            @if(auth()->user()->hasRole(['super_admin']))
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card gradient-info">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <span class="dashboard-icon"><i class="mdi mdi-account"></i></span>
                        <div class="text-right">
                            <h6 class="font-weight-bold mb-1">Admin</h6>
                            <h2 class="font-light mb-0">{{ $roleStats['admin'] ?? 0 }}</h2>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            @if(auth()->user()->hasAnyRole(['super_admin', 'admin']))
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card gradient-success">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <span class="dashboard-icon"><i class="mdi mdi-account"></i></span>
                        <div class="text-right">
                            <h6 class="font-weight-bold mb-1">National Distributor</h6>
                            <h2 class="font-light mb-0">{{ $roleStats['national_distributor'] ?? 0 }}</h2>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            @if(auth()->user()->hasAnyRole(['super_admin', 'admin', 'national_distributor']))
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card gradient-primary">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <span class="dashboard-icon"><i class="mdi mdi-account"></i></span>
                        <div class="text-right">
                            <h6 class="font-weight-bold mb-1">Super Distributor</h6>
                            <h2 class="font-light mb-0">{{ $roleStats['super_distributor'] ?? 0 }}</h2>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            @if(auth()->user()->hasAnyRole(['super_admin', 'admin', 'national_distributor', 'super_distributor']))
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card gradient-warning">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <span class="dashboard-icon"><i class="mdi mdi-account"></i></span>
                        <div class="text-right">
                            <h6 class="font-weight-bold mb-1">Distributor</h6>
                            <h2 class="font-light mb-0">{{ $roleStats['distributor'] ?? 0 }}</h2>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <div class="col-md-3 mb-3">
                <div class="card dashboard-card gradient-danger">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <span class="dashboard-icon"><i class="mdi mdi-account"></i></span>
                        <div class="text-right">
                            <h6 class="font-weight-bold mb-1">Retailer</h6>
                            <h2 class="font-light mb-0">{{ $roleStats['retailer'] ?? 0 }}</h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ===================== IMEI SECTION ===================== -->
    <div class="bg-white rounded shadow-sm p-4 mb-4">
        <div class="row mb-3">
            <div class="col-12">
                <h4 class="font-weight-bold text-info mb-0">
                    <i class="mdi mdi-cellphone-link"></i> IMEI Statistics
                </h4>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card gradient-info">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <span class="dashboard-icon"><i class="mdi mdi-emoticon"></i></span>
                        <div class="text-right">
                            <h6 class="font-weight-bold mb-1">Total Customers</h6>
                            <h2 class="font-light mb-0">{{ $countData['total_installed_imei'] }}</h2>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card gradient-success">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <span class="dashboard-icon"><i class="mdi mdi-image"></i></span>
                        <div class="text-right">
                            <h6 class="font-weight-bold mb-1">Installed IMEIs</h6>
                            <h2 class="font-light mb-0">{{ $countData['total_installed_imei'] }}</h2>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card gradient-danger">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <span class="dashboard-icon"><i class="mdi mdi-currency-eur"></i></span>
                        <div class="text-right">
                            <h6 class="font-weight-bold mb-1">Deleted IMEIs</h6>
                            <h2 class="font-light mb-0">{{ $countData['total_deleted_imei'] }}</h2>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card gradient-secondary">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <span class="dashboard-icon"><i class="mdi mdi-poll"></i></span>
                        <div class="text-right">
                            <h6 class="font-weight-bold mb-1">Today Installed IMEIs</h6>
                            <h2 class="font-light mb-0">{{ $countData['today_installed_imei'] }}</h2>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card gradient-secondary">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <span class="dashboard-icon"><i class="mdi mdi-poll"></i></span>
                        <div class="text-right">
                            <h6 class="font-weight-bold mb-1">Today Deleted IMEIs</h6>
                            <h2 class="font-light mb-0">{{ $countData['today_deleted_imei'] }}</h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ===================== POINTS SECTION ===================== -->
    <div class="bg-white rounded shadow-sm p-4 mb-4">
        <div class="row mb-3">
            <div class="col-12">
                <h4 class="font-weight-bold text-success mb-0">
                    <i class="mdi mdi-key"></i> Points & Keys
                </h4>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card gradient-success">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <span class="dashboard-icon"><i class="mdi mdi-key"></i></span>
                        <div class="text-right">
                            <h6 class="font-weight-bold mb-1">Total Keys</h6>
                            <h2 class="font-light mb-0">{{ $keyStats['total_keys'] }}</h2>
                        </div>
                    </div>
                </div>
            </div>
            {{-- <div class="col-md-3 mb-3">
                <div class="card dashboard-card gradient-primary">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <span class="dashboard-icon"><i class="mdi mdi-currency-inr"></i></span>
                        <div class="text-right">
                            <h6 class="font-weight-bold mb-1">Price Per Key</h6>
                            <h2 class="font-light mb-0">{{ auth()->user()->price_per_key }}</h2>
                        </div>
                    </div>
                </div>
            </div> --}}
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card gradient-warning">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <span class="dashboard-icon"><i class="mdi mdi-key-minus"></i></span>
                        <div class="text-right">
                            <h6 class="font-weight-bold mb-1">Keys Left</h6>
                            <h2 class="font-light mb-0">{{ $keyStats['remaining_keys'] }}</h2>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card gradient-danger">
                    <div class="card-body d-flex align-items-center justify-content-between">
                        <span class="dashboard-icon"><i class="mdi mdi-key-change"></i></span>
                        <div class="text-right">
                            <h6 class="font-weight-bold mb-1">Keys Used</h6>
                            <h2 class="font-light mb-0">{{ $keyStats['used_keys'] }}</h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
@endsection
