@extends('backend.layouts.app')

@section('title', 'Transaction Details - ' . $user->username)

@section('content')

@php 
    $required = true;
@endphp

<!-- ============================================================== -->
<!-- Container fluid  -->
<!-- ============================================================== -->
<div class="container-fluid">
    <div class="row">@include('backend.default_alert')</div>

    <!-- User Info Card -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user"></i> {{ $user->username }} - Transaction History
                        </h5>
                        <small class="text-muted">{{ $user->email }} | {{ $user->mobile }}</small>
                    </div>
                    <div>
                        <a class="btn btn-secondary" href="{{ route('admin.users.index') }}">
                            <i class="fa fa-arrow-left"></i> Back to Users
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-key fa-2x mb-2"></i>
                                    <h4>{{ $user->total_keys ?? 0 }}</h4>
                                    <small>Total Keys</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-clock fa-2x mb-2"></i>
                                    <h4>{{ $user->used_keys ?? 0 }}</h4>
                                    <small>Used Keys</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                                    <h4>{{ max(($user->total_keys ?? 0) - ($user->used_keys ?? 0), 0) }}</h4>
                                    <small>Available Keys</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-exchange-alt fa-2x mb-2"></i>
                                    <h4>{{ $transactions->total() }}</h4>
                                    <small>Total Transactions</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter"></i> Transaction Filters
                        <button type="button" class="btn btn-sm btn-outline-secondary float-right" id="toggleTransactionFilters">
                            <i class="fas fa-chevron-up"></i> Collapse
                        </button>
                    </h5>
                </div>
                <div class="card-body" id="transactionFilterSection">
                    <form action="{{ route('admin.users.transactions', $user->id) }}" method="GET" class="w-100">
                        <div class="row">
                            <!-- Transaction Type -->
                            <div class="col-md-6 col-lg-3">
                                <div class="form-group">
                                    <label for="action" class="form-label">
                                        <i class="fas fa-exchange-alt"></i> Transaction Type
                                    </label>
                                    <select name="action" id="action" class="form-control">
                                        <option value="">All Types</option>
                                        <option value="add" {{ request('action') == 'add' ? 'selected' : '' }}>Credit (Add Keys)</option>
                                        <option value="subtract" {{ request('action') == 'subtract' ? 'selected' : '' }}>Debit (Subtract Keys)</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Payment Type -->
                            {{-- <div class="col-md-6 col-lg-3">
                                <div class="form-group">
                                    <label for="payment_type" class="form-label">
                                        <i class="fas fa-credit-card"></i> Payment Type
                                    </label>
                                    <select name="payment_type" id="payment_type" class="form-control">
                                        <option value="">All Payment Types</option>
                                        <option value="credit" {{ request('payment_type') == 'credit' ? 'selected' : '' }}>Credit</option>
                                        <option value="debit" {{ request('payment_type') == 'debit' ? 'selected' : '' }}>Debit</option>
                                    </select>
                                </div>
                            </div> --}}

                            <!-- Date From -->
                            <div class="col-md-6 col-lg-3">
                                <div class="form-group">
                                    <label for="date_from" class="form-label">
                                        <i class="fas fa-calendar-alt"></i> Date From
                                    </label>
                                    <input type="date" 
                                           name="date_from" 
                                           id="date_from"
                                           class="form-control" 
                                           value="{{ request('date_from') }}">
                                </div>
                            </div>

                            <!-- Date To -->
                            <div class="col-md-6 col-lg-3">
                                <div class="form-group">
                                    <label for="date_to" class="form-label">
                                        <i class="fas fa-calendar-alt"></i> Date To
                                    </label>
                                    <input type="date" 
                                           name="date_to" 
                                           id="date_to"
                                           class="form-control" 
                                           value="{{ request('date_to') }}">
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2 justify-content-between">
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i> Filter
                                            </button>
                                            <a href="{{ route('admin.users.transactions', $user->id) }}" class="btn btn-secondary">
                                                <i class="fas fa-times"></i> Clear
                                            </a>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <button type="button" class="btn btn-info" id="exportTransactionsBtn">
                                                <i class="fas fa-download"></i> Export Excel
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Transactions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list-alt"></i> Transaction Details
                        </h5>
                        <small class="text-muted">Total: {{ $transactions->total() }} {{ $transactions->total() == 1 ? 'transaction' : 'transactions' }}</small>
                    </div>
                </div>
                <div class="card-body">
                    @if($transactions->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th width="8%">
                                            <i class="fas fa-hashtag"></i> Transaction #
                                        </th>
                                        <th width="12%">
                                            <i class="fas fa-user"></i> From
                                        </th>
                                        <th width="12%">
                                            <i class="fas fa-user-check"></i> To
                                        </th>
                                        <th width="12%">
                                            <i class="fas fa-calendar"></i> Date & Time
                                        </th>
                                        <th width="8%">
                                            <i class="fas fa-exchange-alt"></i> Type
                                        </th>
                                        {{-- <th width="8%">
                                            <i class="fas fa-credit-card"></i> Payment
                                        </th> --}}
                                        <th width="8%">
                                            <i class="fas fa-key"></i> Keys
                                        </th>
                                        <th width="8%">
                                            <i class="fas fa-calculator"></i> Total Keys
                                        </th>
                                        <th width="8%">
                                            <i class="fas fa-minus-circle"></i> Keys Used
                                        </th>
                                        <th width="8%">
                                            <i class="fas fa-plus-circle"></i> Keys Left
                                        </th>
                                        <th width="8%">
                                            <i class="fas fa-comment"></i> Remark
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($transactions as $key => $transaction)
                                    <tr>
                                        <td>
                                            <span class="badge badge-primary">
                                                {{ $transaction->transaction_number }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px; font-size: 12px;">
                                                    {{ strtoupper(substr($transaction->changedBy->username ?? 'SYS', 0, 2)) }}
                                                </div>
                                                <div>
                                                    <strong>{{ $transaction->changedBy->username ?? 'System' }}</strong>
                                                    <br><small class="text-muted">{{ $transaction->changedBy->email ?? 'system@admin' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px; font-size: 12px;">
                                                    {{ strtoupper(substr($transaction->assignedUser->username ?? 'N/A', 0, 2)) }}
                                                </div>
                                                <div>
                                                    <strong>{{ $transaction->assignedUser->username ?? 'N/A' }}</strong>
                                                    <br><small class="text-muted">{{ $transaction->assignedUser->email ?? 'N/A' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                {{ $transaction->created_at->format('M d, Y') }}
                                                <br><small class="text-muted">{{ $transaction->created_at->format('h:i A') }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            @if($transaction->action == 'add')
                                                <span class="badge badge-success">
                                                    <i class="fas fa-plus"></i> Credit
                                                </span>
                                            @else
                                                <span class="badge badge-danger">
                                                    <i class="fas fa-minus"></i> Debit
                                                </span>
                                            @endif
                                        </td>
                                        {{-- <td>
                                            @if($transaction->payment_type)
                                                <span class="badge badge-info">
                                                    {{ ucfirst($transaction->payment_type) }}
                                                </span>
                                            @else
                                                <span class="text-muted">N/A</span>
                                            @endif
                                        </td> --}}
                                        <td>
                                            <div class="text-center">
                                                <strong class="{{ $transaction->action == 'add' ? 'text-success' : 'text-danger' }}">
                                                    {{ $transaction->action == 'add' ? '+' : '-' }}{{ $transaction->quantity }}
                                                </strong>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                <strong>{{ $transaction->total_after_change }}</strong>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                <span class="text-warning">
                                                    {{ $user->used_keys ?? 0 }}
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                <span class="text-success font-weight-bold">
                                                    {{ max(($transaction->total_after_change ?? 0) - ($user->used_keys ?? 0), 0) }}
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            @if($transaction->note)
                                                <span title="{{ $transaction->note }}" class="text-truncate d-inline-block" style="max-width: 100px;">
                                                    {{ $transaction->note }}
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                    </tr>   
                                    @endforeach                                         
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="pagination-info">
                                <small class="text-muted">
                                    Showing {{ $transactions->firstItem() ?? 0 }} to {{ $transactions->lastItem() ?? 0 }} of {{ $transactions->total() }} results
                                </small>
                            </div>
                            <div>
                                {{ $transactions->appends(request()->query())->links() }}
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No transactions found</h5>
                            <p class="text-muted">
                                @if(request()->hasAny(['action', 'date_from', 'date_to']))
                                    Try adjusting your filter criteria or 
                                    <a href="{{ route('admin.users.transactions', $user->id) }}" class="text-primary">clear all filters</a>
                                @else
                                    No key transfer transactions have been recorded for this user yet.
                                @endif
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
  window.transactionConfig = {
    exportRoute: "{{ route('admin.users.export-transactions', $user->id) }}",
    userId: {{ $user->id }},
    userName: "{{ $user->username }}"
  };
</script>

<!-- ============================================================== -->
<!-- End Container fluid  -->
<!-- ============================================================== -->
@endsection

@section('styles')
    <link rel="stylesheet" href="{{ asset('backend/assets/css/transactions.css') }}">
@endsection

@section('footer_js')
    <script src="{{ asset('backend/assets/js/transactions.js') }}"></script>
@endsection
