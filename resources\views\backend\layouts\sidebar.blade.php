<!-- ============================================================== -->
<!-- Left Sidebar - style you can find in sidebar.scss  -->
<!-- ============================================================== -->

@php
    $user = Auth::user();
@endphp

<aside class="left-sidebar">
    <!-- Sidebar scroll-->
    <div class="scroll-sidebar">
        <!-- Sidebar navigation-->
        <nav class="sidebar-nav">
            <ul id="sidebarnav">
                <!--<li class="nav-small-cap">
                    <i class="mdi mdi-dots-horizontal"></i>
                    <span class="hide-menu">Personal</span>
                </li> -->
                @if($user->hasRole('super_admin'))
				<li class="sidebar-item">
					<a href="{{ route('admin.dashboard') }}" class="sidebar-link">
						<i class="mdi mdi-adjust"></i>
						<span class="hide-menu"> Dashboard </span>
					</a>
				</li>
				@elseif($user->hasAnyRole(['admin', 'national_distributor', 'super_distributor', 'distributor']))
				<li class="sidebar-item">
					<a href="{{ route('admin.admin-dashboard') }}" class="sidebar-link">
						<i class="mdi mdi-adjust"></i>
						<span class="hide-menu"> Dashboard </span>
					</a>
				</li>
				@endif
				
				@can('super-admin-access')
				
                <li class="sidebar-item">
                    <a class="sidebar-link has-arrow waves-effect waves-dark" href="javascript:void(0)" aria-expanded="false">
                        <i class="mdi mdi-qrcode"></i>
                        <span class="hide-menu">QR Code Manager</span>
                    </a>
                    <ul aria-expanded="false" class="collapse  first-level">                        
						<li class="sidebar-item">
                            <a href="{{ route('admin.phone-qr') }}" class="sidebar-link">
                                <i class="mdi mdi-upload"></i>
                                <span class="hide-menu"> Upload QR Code </span>
                            </a>
                        </li>						
                    </ul>
                </li>
				
				<li class="sidebar-item">
                    <a class="sidebar-link has-arrow waves-effect waves-dark" href="javascript:void(0)" aria-expanded="false">
                        <i class="mdi mdi-apps"></i>
                        <span class="hide-menu">App Manager</span>
                    </a>
                    <ul aria-expanded="false" class="collapse  first-level">                        
						<li class="sidebar-item">
                            <a href="{{ route('admin.app-category') }}" class="sidebar-link">
                                <i class="mdi mdi-apps"></i>
                                <span class="hide-menu"> Category </span>
                            </a>
                        </li>
						<li class="sidebar-item">
                            <a href="{{ route('admin.app') }}" class="sidebar-link">
                                <i class="mdi mdi-apps"></i>
                                <span class="hide-menu"> APP </span>
                            </a>
                        </li>
                    </ul>
                </li>
				
				<li class="sidebar-item">
                    <a class="sidebar-link has-arrow waves-effect waves-dark" href="javascript:void(0)" aria-expanded="false">
                        <i class="mdi mdi-settings"></i>
                        <span class="hide-menu">Settings</span>
                    </a>
                    <ul aria-expanded="false" class="collapse  first-level">
                        <li class="sidebar-item">
                            <a href="{{ route('admin.domain-whitelist') }}" class="sidebar-link">
                                <i class="mdi mdi-server-network"></i>
                                <span class="hide-menu"> Domain Whitelist </span>
                            </a>
                        </li>						
						<li class="sidebar-item">
                            <a href="{{ route('admin.settings') }}" class="sidebar-link">
                                <i class="mdi mdi-apps"></i>
                                <span class="hide-menu">App Settings </span>
                            </a>
                        </li>
                    </ul>
                </li>
				
				
				<li class="sidebar-item">
                    <a href="{{ route('admin.users.index') }}" class="sidebar-link">
                                <i class="mdi mdi-account"></i>
                                <span class="hide-menu"> Admins </span>
                            </a>
                </li>
				
				<li class="sidebar-item">
                    <a href="{{ route('admin.agents.index') }}" class="sidebar-link">
                                <i class="mdi mdi-account"></i>
                                <span class="hide-menu"> Agents </span>
                            </a>
                </li>
				
				@endcan
				
				@canany(['super-admin-access', 'agent-access'])
				<li class="sidebar-item">
                    <a href="{{ route('admin.imei') }}" class="sidebar-link">
                                <i class="mdi mdi-cellphone"></i>
                                <span class="hide-menu"> IMEI </span>
                            </a>
                </li>
				@endcanany
				
				@can('admin-access')
                <li class="sidebar-item">
                    <a href="{{ route('admin.users.index') }}" class="sidebar-link">
                                <i class="mdi mdi-account"></i>
                                <span class="hide-menu"> Users </span>
                            </a>
                </li>
                @endcan
				
            </ul>
        </nav>
        <!-- End Sidebar navigation -->
    </div>
    <!-- End Sidebar scroll-->
</aside>
<!-- ============================================================== -->
<!-- End Left Sidebar - style you can find in sidebar.scss  -->
<!-- ============================================================== -->

<!-- ============================================================== -->
<!-- Page wrapper  -->
<!-- ============================================================== -->
<div class="page-wrapper">