<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\KeysLog;
use App\Models\Country;
use App\Http\Requests\Backend\RetailerRequest;

class RetailerController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
		$query = User::role('retailer');

		if(!auth()->user()->hasRole('super_admin')) {
			$query->where('admin_id', auth()->id());
		}
		
		if ($request->has('name') && !empty($request->name)) {
			$query->where('username', 'LIKE', "%{$request->name}%");
		}
		
		if ($request->has('email') && !empty($request->email)) {
			$query->where('email', 'LIKE', "%{$request->email}%");
		}
		
		if ($request->has('mobile') && !empty($request->mobile)) {
			$query->where('mobile', 'LIKE', "%{$request->mobile}%");
		}
		
		if (!empty($request->reg_date)) {
			$query->whereDate('created_at', $request->reg_date);
		}
		
		$data = $query->orderBy('id', 'desc')->paginate(20);
		
        return view('backend.retailers.index', compact('data'));
    }
	
	/**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
		$countries = Country::orderBy('name')->pluck('name', 'id');
        return view('backend.retailers.create', compact('countries'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(RetailerRequest $request)
    {      
        $input = $request->only(['username', 'email', 'mobile',
								 'address', 'country', 'city', 'state', 'zip', 'pincode',
            					'total_keys', 'price_per_key', 'total_price'
        ]);
		
		$admin = auth()->user();
		
		// Check if admin has enough available keys
		$availableKeys = $admin->total_keys - $admin->used_keys;
		
		if ($request->total_keys > $availableKeys) {
			return back()->withErrors(['error' => 'Not enough available keys.']);
		}

        $input['plan'] = $admin->plan;
		$input['password'] = \Hash::make($request->password);
		$input['admin_id'] = $admin->id;
        //$input['is_admin'] = 0;
        //$input['role'] = 'retailer';

        $user = User::create($input);
		
		$user->assignRole('retailer');
		
		// Update admin used_keys
    	$admin->increment('used_keys', $request->total_keys);
        
        return redirect()->route('admin.retailers.index')->with('alert-success', 'Retailer created successfully.');
    }
	
	/**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $user = User::where(['id'=>$id, 'admin_id'=>auth()->user()->id])->first();
        
        return view('backend.retailers.show', compact('user'));
    }
	
	/*
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function changeStatus(Request $request, $id) {
		
        $user = User::where(['id'=>$id, 'admin_id'=>auth()->user()->id])->first();
		
        if(empty($user)){
            abort(404);
        }
		
        $user->status = $request->input('status');
		
        if($user->save()){
            return ['success' => true, 'message' => $request->input('status') == 1 ?'This Record activated successfully.' : "This Record deactivated successfully."];
        }else{
            return ['success' => false, 'message' => 'Your process failed. please try again!!'];
        }
		
    }
	
	public function manageKeys($id)
    {
        $user = User::where(['id'=>$id, 'admin_id'=>auth()->user()->id])->first();
        
        return view('backend.retailers.manage_keys', compact('user'));
    }
	
	public function showKeys($id)
    {
        $logs = KeysLog::where(['retailer_id'=>$id])->paginate();
        
        return view('backend.retailers.show_keys_log', compact('logs'));
    }
	
	public function updateKeys(Request $request, $id)
	{
		$request->validate([
			'action' => 'required|in:add,subtract',
			'quantity' => 'required|integer|min:1',
			'note' => 'nullable|string'
		]);
		
		try {

			$admin = auth()->user();
			$retailer = User::where(['id'=>$id, 'admin_id'=>auth()->user()->id])->first();

			$quantity = $request->quantity;

			if ($request->action == 'add') {
				$availableKeys = $admin->total_keys - $admin->used_keys;
				
				if ($quantity > $availableKeys) {
					return back()->withErrors(['error' => 'Not enough available keys.']);
				}

				$retailer->increment('total_keys', $quantity);
				$admin->increment('used_keys', $quantity);
				
			} else {
				
				$remainingKeys = $retailer->total_keys - $retailer->used_keys;
				
				if ($quantity > $remainingKeys) {
					return back()->withErrors(['error' => 'Retailer does not have enough keys to subtract.']);
				}

				$retailer->decrement('total_keys', $quantity);
				$admin->decrement('used_keys', $quantity); // Optional: if you want keys to be returned
			}
			

			KeysLog::create([
				'retailer_id' => $retailer->id,
				'changed_by' => auth()->id(), // current admin
				'action' => $request->action,
				'quantity' => $quantity,
				'total_after_change' => $retailer->total_keys,
				'note' => $request->note
			]);

			return redirect()->route('admin.retailers.index')->with('alert-success', 'Keys Updated successfully.');
		} catch(Exception $e) {
			return redirect()->route('admin.retailers.index')->with('alert-danger', $e->getMessage());
		}
	}
	
}
