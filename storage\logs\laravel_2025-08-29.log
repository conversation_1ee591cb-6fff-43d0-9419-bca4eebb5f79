[2025-08-29 14:59:15] local.ERROR: Route [admin.retailers.index] not defined. (View: C:\php-8.2\htdocs\trackmyemi\resources\views\backend\layouts\sidebar.blade.php) {"view":{"view":"C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php","data":[]},"userId":39,"exception":"[object] (Facade\\Ignition\\Exceptions\\ViewException(code: 0): Route [admin.retailers.index] not defined. (View: C:\\php-8.2\\htdocs\\trackmyemi\\resources\\views\\backend\\layouts\\sidebar.blade.php) at C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:444)
[stacktrace]
#0 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(739): Illuminate\\Routing\\UrlGenerator->route('admin.retailers...', Array, true)
#1 C:\\php-8.2\\htdocs\\trackmyemi\\resources\\views/backend/layouts/sidebar.blade.php(128): route('admin.retailers...')
#2 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\php-8.2\\\\htdo...')
#3 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\php-8.2\\\\htdo...', Array)
#5 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#6 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Livewire\\CompilerEngineForIgnition->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#7 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#8 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#9 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Livewire\\CompilerEngineForIgnition->get('C:\\\\php-8.2\\\\htdo...', Array)
#10 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#11 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#12 C:\\php-8.2\\htdocs\\trackmyemi\\resources\\views/backend/layouts/app.blade.php(17): Illuminate\\View\\View->render()
#13 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\php-8.2\\\\htdo...')
#14 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#15 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\php-8.2\\\\htdo...', Array)
#16 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#17 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Livewire\\CompilerEngineForIgnition->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#18 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#19 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#20 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Livewire\\CompilerEngineForIgnition->get('C:\\\\php-8.2\\\\htdo...', Array)
#21 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#22 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#23 C:\\php-8.2\\htdocs\\trackmyemi\\resources\\views/backend/admin-dashboard/index.blade.php(228): Illuminate\\View\\View->render()
#24 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\php-8.2\\\\htdo...')
#25 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#26 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\php-8.2\\\\htdo...', Array)
#27 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#28 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Livewire\\CompilerEngineForIgnition->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#29 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#30 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#31 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Livewire\\CompilerEngineForIgnition->get('C:\\\\php-8.2\\\\htdo...', Array)
#32 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#33 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#34 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#35 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#36 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#37 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#38 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#39 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\php-8.2\\htdocs\\trackmyemi\\app\\Http\\Middleware\\AdminMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#52 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#60 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#61 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#62 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#63 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#82 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#83 C:\\php-8.2\\htdocs\\trackmyemi\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#84 C:\\php-8.2\\htdocs\\trackmyemi\\server.php(21): require_once('C:\\\\php-8.2\\\\htdo...')
#85 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [admin.retailers.index] not defined. at C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:444)
[stacktrace]
#0 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(739): Illuminate\\Routing\\UrlGenerator->route('admin.retailers...', Array, true)
#1 C:\\php-8.2\\htdocs\\trackmyemi\\storage\\framework\\views\\995aa113fbf7a192e57b7d5ded86020e337d0822.php(128): route('admin.retailers...')
#2 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\php-8.2\\\\htdo...')
#3 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\php-8.2\\\\htdo...', Array)
#5 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#6 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Livewire\\CompilerEngineForIgnition->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#7 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#8 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#9 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Livewire\\CompilerEngineForIgnition->get('C:\\\\php-8.2\\\\htdo...', Array)
#10 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#11 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#12 C:\\php-8.2\\htdocs\\trackmyemi\\storage\\framework\\views\\c6ada84326215ca4dc702bcac51086fcae866d3b.php(17): Illuminate\\View\\View->render()
#13 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\php-8.2\\\\htdo...')
#14 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#15 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\php-8.2\\\\htdo...', Array)
#16 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#17 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Livewire\\CompilerEngineForIgnition->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#18 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#19 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#20 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Livewire\\CompilerEngineForIgnition->get('C:\\\\php-8.2\\\\htdo...', Array)
#21 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#22 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#23 C:\\php-8.2\\htdocs\\trackmyemi\\storage\\framework\\views\\a2b0182e5bb1cec977c2ccd650659f4033269270.php(228): Illuminate\\View\\View->render()
#24 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\php-8.2\\\\htdo...')
#25 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#26 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\php-8.2\\\\htdo...', Array)
#27 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#28 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Livewire\\CompilerEngineForIgnition->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#29 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#30 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#31 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Livewire\\CompilerEngineForIgnition->get('C:\\\\php-8.2\\\\htdo...', Array)
#32 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#33 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#34 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#35 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#36 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#37 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#38 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#39 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\php-8.2\\htdocs\\trackmyemi\\app\\Http\\Middleware\\AdminMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#52 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#60 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#61 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#62 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#63 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#82 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#83 C:\\php-8.2\\htdocs\\trackmyemi\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#84 C:\\php-8.2\\htdocs\\trackmyemi\\server.php(21): require_once('C:\\\\php-8.2\\\\htdo...')
#85 {main}
"} 
[2025-08-29 15:00:35] local.ERROR: Route [admin.retailers.index] not defined. (View: C:\php-8.2\htdocs\trackmyemi\resources\views\backend\layouts\sidebar.blade.php) {"view":{"view":"C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php","data":[]},"userId":39,"exception":"[object] (Facade\\Ignition\\Exceptions\\ViewException(code: 0): Route [admin.retailers.index] not defined. (View: C:\\php-8.2\\htdocs\\trackmyemi\\resources\\views\\backend\\layouts\\sidebar.blade.php) at C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:444)
[stacktrace]
#0 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(739): Illuminate\\Routing\\UrlGenerator->route('admin.retailers...', Array, true)
#1 C:\\php-8.2\\htdocs\\trackmyemi\\resources\\views/backend/layouts/sidebar.blade.php(128): route('admin.retailers...')
#2 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\php-8.2\\\\htdo...')
#3 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\php-8.2\\\\htdo...', Array)
#5 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#6 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Livewire\\CompilerEngineForIgnition->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#7 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#8 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#9 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Livewire\\CompilerEngineForIgnition->get('C:\\\\php-8.2\\\\htdo...', Array)
#10 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#11 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#12 C:\\php-8.2\\htdocs\\trackmyemi\\resources\\views/backend/layouts/app.blade.php(17): Illuminate\\View\\View->render()
#13 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\php-8.2\\\\htdo...')
#14 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#15 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\php-8.2\\\\htdo...', Array)
#16 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#17 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Livewire\\CompilerEngineForIgnition->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#18 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#19 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#20 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Livewire\\CompilerEngineForIgnition->get('C:\\\\php-8.2\\\\htdo...', Array)
#21 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#22 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#23 C:\\php-8.2\\htdocs\\trackmyemi\\resources\\views/backend/admin-dashboard/index.blade.php(228): Illuminate\\View\\View->render()
#24 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\php-8.2\\\\htdo...')
#25 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#26 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\php-8.2\\\\htdo...', Array)
#27 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#28 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Livewire\\CompilerEngineForIgnition->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#29 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#30 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#31 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Livewire\\CompilerEngineForIgnition->get('C:\\\\php-8.2\\\\htdo...', Array)
#32 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#33 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#34 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#35 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#36 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#37 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#38 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#39 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\php-8.2\\htdocs\\trackmyemi\\app\\Http\\Middleware\\AdminMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#52 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#60 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#61 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#62 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#63 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#82 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#83 C:\\php-8.2\\htdocs\\trackmyemi\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#84 C:\\php-8.2\\htdocs\\trackmyemi\\server.php(21): require_once('C:\\\\php-8.2\\\\htdo...')
#85 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [admin.retailers.index] not defined. at C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:444)
[stacktrace]
#0 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(739): Illuminate\\Routing\\UrlGenerator->route('admin.retailers...', Array, true)
#1 C:\\php-8.2\\htdocs\\trackmyemi\\storage\\framework\\views\\995aa113fbf7a192e57b7d5ded86020e337d0822.php(128): route('admin.retailers...')
#2 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\php-8.2\\\\htdo...')
#3 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\php-8.2\\\\htdo...', Array)
#5 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#6 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Livewire\\CompilerEngineForIgnition->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#7 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#8 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#9 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Livewire\\CompilerEngineForIgnition->get('C:\\\\php-8.2\\\\htdo...', Array)
#10 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#11 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#12 C:\\php-8.2\\htdocs\\trackmyemi\\storage\\framework\\views\\c6ada84326215ca4dc702bcac51086fcae866d3b.php(17): Illuminate\\View\\View->render()
#13 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\php-8.2\\\\htdo...')
#14 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#15 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\php-8.2\\\\htdo...', Array)
#16 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#17 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Livewire\\CompilerEngineForIgnition->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#18 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#19 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#20 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Livewire\\CompilerEngineForIgnition->get('C:\\\\php-8.2\\\\htdo...', Array)
#21 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#22 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#23 C:\\php-8.2\\htdocs\\trackmyemi\\storage\\framework\\views\\a2b0182e5bb1cec977c2ccd650659f4033269270.php(228): Illuminate\\View\\View->render()
#24 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\php-8.2\\\\htdo...')
#25 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#26 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\php-8.2\\\\htdo...', Array)
#27 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#28 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Livewire\\CompilerEngineForIgnition->evaluatePath('C:\\\\php-8.2\\\\htdo...', Array)
#29 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#30 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\php-8.2\\\\htdo...', Array)
#31 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Livewire\\CompilerEngineForIgnition->get('C:\\\\php-8.2\\\\htdo...', Array)
#32 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#33 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#34 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#35 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#36 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#37 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#38 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#39 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\php-8.2\\htdocs\\trackmyemi\\app\\Http\\Middleware\\AdminMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#52 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#60 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#61 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#62 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#63 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#82 C:\\php-8.2\\htdocs\\trackmyemi\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#83 C:\\php-8.2\\htdocs\\trackmyemi\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#84 C:\\php-8.2\\htdocs\\trackmyemi\\server.php(21): require_once('C:\\\\php-8.2\\\\htdo...')
#85 {main}
"} 
